#!/usr/bin/env python3
"""
Comprehensive test suite for Flexible Pattern Parser
UNBREAKABLE RULE: Uses ONLY real market data from /tests/RealTestData
"""

import unittest
import os
import sys
import pandas as pd
import numpy as np

# Add src directory to path for proper imports
src_path = os.path.join(os.path.dirname(__file__), '..', 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from flexible_pattern_parser import (
    FlexiblePatternParser, 
    TradingPattern, 
    FlexiblePatternParseError,
    parse_flexible_patterns
)


class TestFlexiblePatternParser(unittest.TestCase):
    """Test suite for FlexiblePatternParser"""
    
    def setUp(self):
        """Set up test fixtures with real market data"""
        # Load real market data for testing
        data_path = os.path.join(os.path.dirname(__file__), 'RealTestData', 'dax_200_bars.csv')
        self.assertTrue(os.path.exists(data_path), f"Real test data not found: {data_path}")
        
        # Read and process real market data
        df = pd.read_csv(data_path)
        df = df.drop_duplicates()
        df['DateTime'] = pd.to_datetime(df['DateTime'])
        df.set_index('DateTime', inplace=True)
        
        # Select first 50 rows for testing
        self.sample_data = df[['Open', 'High', 'Low', 'Close', 'Volume']].head(50)
        
        self.parser = FlexiblePatternParser()
        
        # Sample LLM response for testing
        self.sample_llm_response = """
        Pattern Analysis Results:
        
        name: Opening Range Breakout Pattern
        entry_logic: Close > High[1] and Volume > 1000
        direction: long
        stop_logic: Low[1] - 10 points
        target_logic: 2% above entry price
        position_size: 1.0
        timeframe: 5min
        
        name: Reversal Pattern
        entry_logic: Close < Low[1] and RSI < 30
        direction: short
        stop_logic: 1.5% above entry
        target_logic: High[1] + 5 points
        position_size: 0.5
        timeframe: 15min
        """
    
    def test_import(self):
        """Test module import"""
        self.assertIsNotNone(FlexiblePatternParser)
        self.assertIsNotNone(TradingPattern)
        self.assertIsNotNone(FlexiblePatternParseError)
        self.assertIsNotNone(parse_flexible_patterns)
    
    def test_no_hardcoded_params(self):
        """Test no hardcoded parameters"""
        module_path = os.path.join(os.path.dirname(__file__), '../src/flexible_pattern_parser.py')
        with open(module_path, 'r') as f:
            content = f.read()
        # Allow config imports but no hardcoded values
        hardcoded_patterns = [line for line in content.split('\n') if '="' in line and 'import' not in line and 'config' not in line]
        self.assertEqual(len(hardcoded_patterns), 0, f'UNBREAKABLE RULE VIOLATION: Hardcoded parameters found: {hardcoded_patterns}')
    
    def test_trading_pattern_dataclass(self):
        """Test TradingPattern dataclass creation and properties"""
        pattern = TradingPattern(
            pattern_id=1,
            name="Test Pattern",
            entry_logic="Close > Open",
            direction="long",
            stop_logic="Low[1]",
            target_logic="2R",
            position_size=1.0,
            timeframe="5min",
            raw_text="test data"
        )
        
        self.assertEqual(pattern.pattern_id, 1)
        self.assertEqual(pattern.name, "Test Pattern")
        self.assertEqual(pattern.entry_logic, "Close > Open")
        self.assertEqual(pattern.direction, "long")
        self.assertEqual(pattern.stop_logic, "Low[1]")
        self.assertEqual(pattern.target_logic, "2R")
        self.assertEqual(pattern.position_size, 1.0)
        self.assertEqual(pattern.timeframe, "5min")
        self.assertEqual(pattern.raw_text, "test data")
    
    def test_parser_initialization(self):
        """Test FlexiblePatternParser initialization"""
        parser = FlexiblePatternParser()
        self.assertEqual(len(parser.patterns), 0)
        self.assertEqual(len(parser.validation_errors), 0)
    
    def test_parse_llm_response_success(self):
        """Test successful parsing of LLM response"""
        patterns = self.parser.parse_llm_response(self.sample_llm_response)
        
        self.assertGreater(len(patterns), 0)
        self.assertIsInstance(patterns[0], TradingPattern)
        
        # Check first pattern
        pattern1 = patterns[0]
        self.assertEqual(pattern1.name, "Opening Range Breakout Pattern")
        self.assertEqual(pattern1.direction, "long")
        self.assertEqual(pattern1.timeframe, "5min")
        self.assertEqual(pattern1.position_size, 1.0)
    
    def test_parse_empty_response(self):
        """Test parsing empty LLM response"""
        patterns = self.parser.parse_llm_response("")
        self.assertEqual(len(patterns), 0)
    
    def test_parse_malformed_response(self):
        """Test parsing malformed LLM response"""
        malformed_response = "This is not a valid pattern response"
        patterns = self.parser.parse_llm_response(malformed_response)
        # Should handle gracefully and return empty list or minimal patterns
        self.assertIsInstance(patterns, list)
    
    def test_extract_field_blocks(self):
        """Test _extract_field_blocks method"""
        test_text = """
        name: Test Pattern
        entry_logic: Close > Open
        direction: long
        """
        
        field_blocks = self.parser._extract_field_blocks(test_text)
        self.assertGreater(len(field_blocks), 0)
        
        # Check that fields are extracted correctly
        field_names = [block['field'] for block in field_blocks]
        self.assertIn('name', field_names)
        self.assertIn('entry_logic', field_names)
        self.assertIn('direction', field_names)
    
    def test_group_fields_into_patterns(self):
        """Test _group_fields_into_patterns method"""
        field_blocks = [
            {'field': 'name', 'value': 'Pattern 1', 'position': 0},
            {'field': 'entry_logic', 'value': 'Close > Open', 'position': 1},
            {'field': 'name', 'value': 'Pattern 2', 'position': 100},
            {'field': 'entry_logic', 'value': 'Close < Open', 'position': 101}
        ]
        
        pattern_groups = self.parser._group_fields_into_patterns(field_blocks)
        self.assertEqual(len(pattern_groups), 2)
        self.assertIn('name', pattern_groups[0])
        self.assertIn('name', pattern_groups[1])
    
    def test_create_pattern_from_fields(self):
        """Test _create_pattern_from_fields method"""
        fields = {
            'name': 'Test Pattern',
            'entry_logic': 'Close > Open',
            'direction': 'long',
            'stop_logic': 'Low[1]',
            'target_logic': '2R',
            'position_size': '1.0',
            'timeframe': '5min'
        }
        
        pattern = self.parser._create_pattern_from_fields(1, fields)
        self.assertIsInstance(pattern, TradingPattern)
        self.assertEqual(pattern.pattern_id, 1)
        self.assertEqual(pattern.name, 'Test Pattern')
        self.assertEqual(pattern.direction, 'long')
    
    def test_generate_python_functions(self):
        """Test generate_python_functions method"""
        # First parse some patterns
        patterns = self.parser.parse_llm_response(self.sample_llm_response)
        self.assertGreater(len(patterns), 0)
        
        # Generate Python functions
        functions = self.parser.generate_python_functions()
        self.assertIsInstance(functions, list)
        
        # Test that functions are callable
        for func in functions:
            self.assertTrue(callable(func))
    
    def test_parse_flexible_patterns_function(self):
        """Test parse_flexible_patterns standalone function"""
        functions = parse_flexible_patterns(self.sample_llm_response)
        self.assertIsInstance(functions, list)
        
        # Test with invalid input
        functions_empty = parse_flexible_patterns("")
        self.assertIsInstance(functions_empty, list)
    
    def test_real_data_only(self):
        """Test that only real market data is used"""
        # Verify we're using real data from RealTestData directory
        self.assertTrue(len(self.sample_data) > 0)
        self.assertIn('Open', self.sample_data.columns)
        self.assertIn('High', self.sample_data.columns)
        self.assertIn('Low', self.sample_data.columns)
        self.assertIn('Close', self.sample_data.columns)
        self.assertIn('Volume', self.sample_data.columns)
        
        # Verify data is realistic (not synthetic)
        self.assertTrue(self.sample_data['High'].min() > 0)
        self.assertTrue(self.sample_data['Volume'].min() >= 0)


if __name__ == '__main__':
    unittest.main()
