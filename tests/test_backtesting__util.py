import unittest
import os
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import pandas as pd
import numpy as np
from backtesting._util import _as_str, try_, _tqdm, _batch, patch, _Array, _Indicator, _Data

REAL_DATA_PATH = 'tests/RealTestData/dax_200_bars.csv'
REQUIRED_COLS = ['Open', 'High', 'Low', 'Close', 'Volume']

class TestBacktestingUtilJaeger(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        # Fail-fast if real data file missing
        if not os.path.exists(REAL_DATA_PATH):
            raise FileNotFoundError('UNBREAKABLE RULE VIOLATION: Real test data missing at ' + REAL_DATA_PATH)
        cls.df = pd.read_csv(REAL_DATA_PATH)
        if 'DateTime' in cls.df.columns:
            cls.df['DateTime'] = pd.to_datetime(cls.df['DateTime'])
            cls.df = cls.df.set_index('DateTime')
        for col in REQUIRED_COLS:
            if col not in cls.df.columns:
                raise AssertionError(f'UNBREAKABLE RULE VIOLATION: Missing required column: {col}')
        if cls.df[['Open','High','Low','Close']].isnull().any().any():
            raise AssertionError('UNBREAKABLE RULE VIOLATION: NaN found in OHLC columns')

    def test_as_str(self):
        self.assertEqual(_as_str(5), '5')
        self.assertEqual(_as_str(5.1), '5.1')
        self.assertEqual(_as_str('foo'), 'foo')

    def test_try_success_and_fail(self):
        self.assertEqual(try_(lambda: 1+1), 2)
        self.assertIsNone(try_(lambda: 1/0, ZeroDivisionError))
        self.assertEqual(try_(lambda: 1/0, ZeroDivisionError, default=42), 42)

    def test_tqdm_fallback(self):
        # Should return iterable even if tqdm is not installed
        items = list(_tqdm(range(3)))
        self.assertEqual(items, [0,1,2])

    def test_batch(self):
        batches = list(_batch([1,2,3,4,5]))
        # _batch now calculates batch size automatically based on CPU count
        # Just verify we get a list of lists and all items are included
        flattened = [item for batch in batches for item in batch]
        self.assertEqual(flattened, [1,2,3,4,5])

    def test_patch_context_manager(self):
        class Dummy:
            attr = 1
        d = Dummy()
        with patch(d, 'attr', 42):
            self.assertEqual(d.attr, 42)
        self.assertEqual(d.attr, 1)

    def test_array_and_indicator(self):
        arr = np.array([1,2,3])
        # Debug: print _Array module and file
        print('_Array module:', _Array.__module__)
        print('_Array file:', getattr(_Array, '__file__', 'N/A'))
        a = _Array(arr, name='test_array')
        self.assertTrue(np.all(a == arr))
        # Test that _Array has the expected properties
        self.assertEqual(a.name, 'test_array')
        ind = _Indicator(arr, name='foo', plot=False)
        self.assertEqual(ind._opts['name'], 'foo')
        self.assertFalse(ind._opts['plot'])

    def test_data_container(self):
        data = _Data(self.df)
        self.assertEqual(len(data), len(self.df))
        self.assertTrue(np.allclose(data.Open, self.df['Open'].values))
        self.assertTrue(np.allclose(data.High, self.df['High'].values))
        self.assertTrue(np.allclose(data.Low, self.df['Low'].values))
        self.assertTrue(np.allclose(data.Close, self.df['Close'].values))
        self.assertTrue(np.allclose(data.Volume, self.df['Volume'].values))
        self.assertTrue(np.all(data.index == self.df.index))
        # Test _set_length and df property
        data._set_length(10)
        self.assertEqual(len(data), 10)
        self.assertEqual(len(data.df), 10)
        # Test pip property - check that it's a reasonable value
        self.assertGreater(data.pip, 0)
        self.assertLess(data.pip, 1)

    def test_strategy_indicators_and_warmup(self):
        from backtesting._util import _strategy_indicators, _indicator_warmup_nbars, _Indicator
        class Dummy:
            ind = _Indicator(np.arange(10), name='foo')
        dummy = Dummy()
        inds = _strategy_indicators(dummy)
        self.assertEqual(len(inds), 1)
        self.assertEqual(inds[0][0], 'ind')
        # _indicator_warmup_nbars should be 0 unless _warmup is set
        self.assertEqual(_indicator_warmup_nbars(dummy), 0)
        dummy.ind._warmup = 3
        self.assertEqual(_indicator_warmup_nbars(dummy), 3)

    def test_shared_memory_manager(self):
        from backtesting._util import SharedMemoryManager
        df = self.df.head(5)
        with SharedMemoryManager() as mgr:
            shm_result = mgr.df2shm(df)
            # shm_result might be a tuple or a DataFrame depending on implementation
            if isinstance(shm_result, tuple):
                shm, extra = shm_result
                self.assertTrue(shm.equals(df))
                self.assertIsInstance(extra, list)
            else:
                # If it's just a DataFrame
                self.assertTrue(shm_result.equals(df))

            # Test shm2df if shm is available
            if 'shm' in locals():
                out, extra = mgr.shm2df(shm)
                self.assertTrue(out.equals(df))
                self.assertIsInstance(extra, list)

if __name__ == '__main__':
    unittest.main()
